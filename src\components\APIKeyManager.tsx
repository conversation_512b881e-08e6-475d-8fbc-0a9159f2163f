import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>Off, Check, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { aiService } from "@/services/aiService";
import { toast } from "sonner";

interface APIKeyConfig {
  provider: 'huggingface' | 'openai' | 'replicate';
  name: string;
  description: string;
  signupUrl: string;
  docsUrl: string;
  free: boolean;
}

const apiConfigs: APIKeyConfig[] = [
  {
    provider: 'huggingface',
    name: 'Hugging Face',
    description: 'Free tier available. Best for diverse AI models.',
    signupUrl: 'https://huggingface.co/join',
    docsUrl: 'https://huggingface.co/docs/api-inference',
    free: true,
  },
  {
    provider: 'replicate',
    name: 'Replicate',
    description: 'Pay-per-use. High-quality models with fast processing.',
    signupUrl: 'https://replicate.com',
    docsUrl: 'https://replicate.com/docs',
    free: false,
  },
  {
    provider: 'openai',
    name: 'OpenAI DALL-E',
    description: 'Premium quality with DALL-E 3. Paid service.',
    signupUrl: 'https://platform.openai.com/signup',
    docsUrl: 'https://platform.openai.com/docs',
    free: false,
  },
];

export const APIKeyManager = () => {
  const [keys, setKeys] = useState<Record<string, string>>({});
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    // Load existing keys status
    const status: Record<string, string> = {};
    apiConfigs.forEach(config => {
      if (aiService.hasApiKey(config.provider)) {
        status[config.provider] = '••••••••';
      }
    });
    setKeys(status);
  }, []);

  const handleSaveKey = (provider: string, key: string) => {
    if (!key.trim()) {
      toast.error('Please enter a valid API key');
      return;
    }

    aiService.setApiKey(provider as any, key.trim());
    setKeys(prev => ({ ...prev, [provider]: '••••••••' }));
    toast.success(`${provider} API key saved successfully`);
  };

  const handleRemoveKey = (provider: string) => {
    aiService.setApiKey(provider as any, '');
    setKeys(prev => {
      const newKeys = { ...prev };
      delete newKeys[provider];
      return newKeys;
    });
    toast.success(`${provider} API key removed`);
  };

  const toggleShowKey = (provider: string) => {
    setShowKeys(prev => ({ ...prev, [provider]: !prev[provider] }));
  };

  const getKeyStatus = (provider: string) => {
    return aiService.hasApiKey(provider as any);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Settings className="w-4 h-4 mr-2" />
          API Keys
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="w-5 h-5" />
            AI API Configuration
          </DialogTitle>
          <DialogDescription>
            Configure your AI API keys to enable logo generation. Your keys are stored locally and never sent to our servers.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="setup" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="setup">API Setup</TabsTrigger>
            <TabsTrigger value="pricing">Pricing & Limits</TabsTrigger>
          </TabsList>

          <TabsContent value="setup" className="space-y-6">
            {apiConfigs.map((config) => (
              <Card key={config.provider} className="generation-panel">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {config.name}
                        {config.free && <Badge variant="secondary">Free Tier</Badge>}
                        {getKeyStatus(config.provider) ? (
                          <Badge className="bg-green-500">
                            <Check className="w-3 h-3 mr-1" />
                            Configured
                          </Badge>
                        ) : (
                          <Badge variant="outline">
                            <X className="w-3 h-3 mr-1" />
                            Not Set
                          </Badge>
                        )}
                      </CardTitle>
                      <CardDescription>{config.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <div className="flex-1">
                      <Label htmlFor={`${config.provider}-key`}>API Key</Label>
                      <div className="flex gap-2 mt-1">
                        <div className="relative flex-1">
                          <Input
                            id={`${config.provider}-key`}
                            type={showKeys[config.provider] ? "text" : "password"}
                            placeholder={keys[config.provider] || "Enter your API key..."}
                            value={showKeys[config.provider] ? (aiService.getApiKey(config.provider) || '') : ''}
                            onChange={(e) => {
                              if (showKeys[config.provider]) {
                                // Only update when visible
                                setKeys(prev => ({ ...prev, [config.provider]: e.target.value }));
                              }
                            }}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-1 top-1 h-7 w-7 p-0"
                            onClick={() => toggleShowKey(config.provider)}
                          >
                            {showKeys[config.provider] ? (
                              <EyeOff className="w-3 h-3" />
                            ) : (
                              <Eye className="w-3 h-3" />
                            )}
                          </Button>
                        </div>
                        <Button
                          onClick={() => {
                            const key = showKeys[config.provider] 
                              ? (aiService.getApiKey(config.provider) || keys[config.provider])
                              : keys[config.provider];
                            handleSaveKey(config.provider, key || '');
                          }}
                          disabled={!keys[config.provider] && !showKeys[config.provider]}
                        >
                          Save
                        </Button>
                        {getKeyStatus(config.provider) && (
                          <Button
                            variant="outline"
                            onClick={() => handleRemoveKey(config.provider)}
                          >
                            Remove
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" asChild>
                      <a href={config.signupUrl} target="_blank" rel="noopener noreferrer">
                        Get API Key
                      </a>
                    </Button>
                    <Button variant="outline" size="sm" asChild>
                      <a href={config.docsUrl} target="_blank" rel="noopener noreferrer">
                        Documentation
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="pricing" className="space-y-4">
            <div className="grid gap-4">
              <Card className="generation-panel">
                <CardHeader>
                  <CardTitle>Hugging Face (Recommended for Free Users)</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• Free tier: 1,000 requests/month</li>
                    <li>• Multiple model options</li>
                    <li>• Good quality results</li>
                    <li>• Best for getting started</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="generation-panel">
                <CardHeader>
                  <CardTitle>Replicate (Best Performance)</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• Pay per generation (~$0.01-0.05 per image)</li>
                    <li>• Fast processing</li>
                    <li>• High-quality results</li>
                    <li>• Latest AI models</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="generation-panel">
                <CardHeader>
                  <CardTitle>OpenAI DALL-E (Premium Quality)</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• $0.04 per image (1024x1024)</li>
                    <li>• Highest quality results</li>
                    <li>• Best prompt understanding</li>
                    <li>• Most consistent output</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};