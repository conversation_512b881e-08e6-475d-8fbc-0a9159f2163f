@tailwind base;
@tailwind components;
@tailwind utilities;

/* LogoCraft AI Design System - Professional Brand Identity Platform
All colors, gradients, fonts, and design tokens defined here.
All colors MUST be HSL format for consistency.
*/

@layer base {
  :root {
    /* Brand Colors */
    --background: 240 10% 4%;
    --foreground: 210 40% 98%;
    
    /* Surface Colors */
    --card: 240 10% 6%;
    --card-foreground: 210 40% 98%;
    
    --popover: 240 5% 8%;
    --popover-foreground: 210 40% 98%;
    
    /* Primary Brand Colors - LogoCraft Purple */
    --primary: 263 85% 65%;
    --primary-foreground: 210 40% 98%;
    --primary-glow: 263 85% 75%;
    --primary-variant: 280 85% 60%;
    
    /* Secondary Colors - Professional Blue */
    --secondary: 217 91% 60%;
    --secondary-foreground: 210 40% 98%;
    --secondary-variant: 205 91% 55%;
    
    /* Accent Colors - Creative Orange */
    --accent: 25 95% 60%;
    --accent-foreground: 240 10% 4%;
    --accent-glow: 25 95% 70%;
    
    /* Neutral Colors */
    --muted: 240 5% 15%;
    --muted-foreground: 215 20% 65%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 240 5% 20%;
    --input: 240 5% 15%;
    --ring: 263 85% 65%;

    /* Design System Variables */
    --radius: 0.75rem;
    
    /* Brand Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-variant)));
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)), hsl(var(--secondary-variant)));
    --gradient-accent: linear-gradient(135deg, hsl(var(--accent)), hsl(var(--accent-glow)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 50%, hsl(var(--accent)) 100%);
    --gradient-surface: linear-gradient(180deg, hsl(var(--card)) 0%, hsl(var(--background)) 100%);
    
    /* Professional Shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(var(--primary) / 0.3);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.4);
    --shadow-card: 0 4px 20px -5px hsl(var(--foreground) / 0.1);
    --shadow-floating: 0 25px 50px -12px hsl(var(--foreground) / 0.25);
    
    /* Animation Variables */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --animation-duration: 0.6s;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark theme uses same brand colors with adjusted backgrounds */
    --background: 240 10% 4%;
    --foreground: 210 40% 98%;
    
    --card: 240 10% 6%;
    --card-foreground: 210 40% 98%;
    
    --popover: 240 5% 8%;
    --popover-foreground: 210 40% 98%;
    
    --muted: 240 5% 15%;
    --muted-foreground: 215 20% 65%;
    
    --border: 240 5% 20%;
    --input: 240 5% 15%;
    
    --sidebar-background: 240 5% 8%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 263 85% 65%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 240 5% 15%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 240 5% 20%;
    --sidebar-ring: 263 85% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Hero Components */
  .hero-gradient {
    background: var(--gradient-hero);
  }
  
  .surface-gradient {
    background: var(--gradient-surface);
  }
  
  /* Professional Shadow Classes */
  .shadow-elegant {
    box-shadow: var(--shadow-elegant);
  }
  
  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }
  
  .shadow-floating {
    box-shadow: var(--shadow-floating);
  }
  
  /* Animation Classes */
  .transition-smooth {
    transition: var(--transition-smooth);
  }
  
  .transition-spring {
    transition: var(--transition-spring);
  }
  
  /* Professional Button Variants */
  .btn-hero {
    background: var(--gradient-primary);
    @apply text-primary-foreground font-semibold px-8 py-4 rounded-xl shadow-elegant hover:shadow-glow transition-smooth;
  }
  
  .btn-creative {
    background: var(--gradient-accent);
    @apply text-accent-foreground font-semibold px-6 py-3 rounded-lg transition-spring hover:scale-105;
  }
  
  /* Logo Generation Styles */
  .generation-panel {
    @apply bg-card/50 backdrop-blur-sm border border-border/50 rounded-2xl p-6 shadow-card;
  }
  
  .vector-canvas {
    @apply bg-card border border-border rounded-xl shadow-floating;
  }
}