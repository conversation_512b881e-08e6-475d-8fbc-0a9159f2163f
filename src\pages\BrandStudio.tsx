import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { 
  ArrowLeft, 
  Download, 
  FileText, 
  CreditCard, 
  Mail, 
  Share2,
  Palette,
  Layout,
  FileImage,
  Globe,
  Instagram,
  Facebook,
  Twitter
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";

const BrandStudio = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  
  const [generationProgress, setGenerationProgress] = useState({
    businessCards: 100,
    letterhead: 100,
    socialMedia: 85,
    brandGuide: 60
  });

  const brandAssets = [
    {
      category: "Business Cards",
      icon: CreditCard,
      items: [
        { name: "Standard Business Card", format: "3.5x2 inches", status: "Ready", preview: "card1.png" },
        { name: "Mini Business Card", format: "3.3x2.1 inches", status: "Ready", preview: "card2.png" },
        { name: "Folded Business Card", format: "3.5x4 inches", status: "Ready", preview: "card3.png" }
      ]
    },
    {
      category: "Letterhead & Stationery",
      icon: FileText,
      items: [
        { name: "Letterhead", format: "8.5x11 inches", status: "Ready", preview: "letter1.png" },
        { name: "Envelope", format: "#10 Envelope", status: "Ready", preview: "env1.png" },
        { name: "Invoice Template", format: "8.5x11 inches", status: "Ready", preview: "invoice1.png" }
      ]
    },
    {
      category: "Social Media",
      icon: Share2,
      items: [
        { name: "Facebook Cover", format: "1200x630px", status: "Generating", preview: null },
        { name: "Instagram Profile", format: "1080x1080px", status: "Ready", preview: "insta1.png" },
        { name: "Twitter Header", format: "1500x500px", status: "Ready", preview: "twitter1.png" },
        { name: "LinkedIn Banner", format: "1584x396px", status: "Generating", preview: null }
      ]
    },
    {
      category: "Brand Guidelines",
      icon: FileImage,
      items: [
        { name: "Logo Usage Guide", format: "PDF", status: "Generating", preview: null },
        { name: "Color Palette", format: "PDF", status: "Ready", preview: "colors.png" },
        { name: "Typography Guide", format: "PDF", status: "Generating", preview: null },
        { name: "Brand Voice Guide", format: "PDF", status: "Pending", preview: null }
      ]
    }
  ];

  const socialPlatforms = [
    { name: "Instagram", icon: Instagram, color: "from-pink-500 to-yellow-500", sizes: ["1080x1080", "1080x1920"] },
    { name: "Facebook", icon: Facebook, color: "from-blue-600 to-blue-700", sizes: ["1200x630", "1080x1080"] },
    { name: "Twitter", icon: Twitter, color: "from-blue-400 to-blue-500", sizes: ["1500x500", "1080x1080"] },
    { name: "LinkedIn", icon: Globe, color: "from-blue-700 to-blue-800", sizes: ["1584x396", "1080x1080"] }
  ];

  const handleDownloadAll = () => {
    toast.success("Downloading complete brand package...");
  };

  const handleGenerateAsset = (category: string, item: string) => {
    toast.info(`Generating ${item} for ${category}...`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Ready": return "bg-green-500";
      case "Generating": return "bg-yellow-500";
      case "Pending": return "bg-gray-500";
      default: return "bg-gray-500";
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border/50 bg-card/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate(`/editor/${projectId}`)}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Editor
              </Button>
              <div className="h-6 w-px bg-border" />
              <div>
                <h1 className="font-semibold text-lg">Brand Studio</h1>
                <p className="text-sm text-muted-foreground">TechFlow Complete Brand Package</p>
              </div>
            </div>
            
            <Button 
              onClick={handleDownloadAll}
              className="bg-gradient-primary hover:shadow-glow transition-smooth"
            >
              <Download className="w-4 h-4 mr-2" />
              Download All
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto p-6">
        {/* Progress Overview */}
        <Card className="generation-panel mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Layout className="w-5 h-5 text-primary" />
              Brand Package Generation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {Object.entries(generationProgress).map(([key, progress]) => (
                <div key={key} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                    <span className="text-sm text-muted-foreground">{progress}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="assets" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="assets">Brand Assets</TabsTrigger>
            <TabsTrigger value="social">Social Media</TabsTrigger>
            <TabsTrigger value="guidelines">Brand Guidelines</TabsTrigger>
          </TabsList>
          
          <TabsContent value="assets" className="mt-6">
            <div className="space-y-8">
              {brandAssets.map((category, categoryIndex) => (
                <Card key={categoryIndex} className="generation-panel">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <category.icon className="w-5 h-5 text-primary" />
                      {category.category}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {category.items.map((item, itemIndex) => (
                        <Card key={itemIndex} className="hover:shadow-glow transition-smooth">
                          <CardContent className="p-4">
                            <div className="aspect-[3/2] bg-muted rounded-lg mb-4 flex items-center justify-center">
                              {item.preview ? (
                                <div className="w-full h-full bg-gradient-primary rounded-lg opacity-20" />
                              ) : (
                                <Palette className="w-8 h-8 text-muted-foreground" />
                              )}
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <h4 className="font-medium text-sm">{item.name}</h4>
                                <Badge className={`text-xs ${getStatusColor(item.status)}`}>
                                  {item.status}
                                </Badge>
                              </div>
                              <p className="text-xs text-muted-foreground">{item.format}</p>
                              <div className="flex gap-2">
                                {item.status === "Ready" ? (
                                  <Button size="sm" variant="outline" className="text-xs">
                                    <Download className="w-3 h-3 mr-1" />
                                    Download
                                  </Button>
                                ) : (
                                  <Button 
                                    size="sm" 
                                    variant="outline" 
                                    className="text-xs"
                                    onClick={() => handleGenerateAsset(category.category, item.name)}
                                  >
                                    Generate
                                  </Button>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="social" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {socialPlatforms.map((platform, index) => (
                <Card key={index} className="generation-panel">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <div className={`p-2 rounded-lg bg-gradient-to-r ${platform.color}`}>
                        <platform.icon className="w-5 h-5 text-white" />
                      </div>
                      {platform.name} Assets
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {platform.sizes.map((size, sizeIndex) => (
                      <div key={sizeIndex} className="flex items-center justify-between p-3 border border-border rounded-lg">
                        <div>
                          <p className="font-medium text-sm">{platform.name} Post</p>
                          <p className="text-xs text-muted-foreground">{size}</p>
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            <FileImage className="w-3 h-3 mr-1" />
                            Preview
                          </Button>
                          <Button size="sm">
                            <Download className="w-3 h-3 mr-1" />
                            Download
                          </Button>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="guidelines" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="generation-panel">
                <CardHeader>
                  <CardTitle>Brand Colors</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <div className="aspect-square bg-primary rounded-lg"></div>
                        <div className="text-center">
                          <p className="text-sm font-medium">Primary</p>
                          <p className="text-xs text-muted-foreground">#6366F1</p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="aspect-square bg-secondary rounded-lg"></div>
                        <div className="text-center">
                          <p className="text-sm font-medium">Secondary</p>
                          <p className="text-xs text-muted-foreground">#0F72FF</p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="aspect-square bg-accent rounded-lg"></div>
                        <div className="text-center">
                          <p className="text-sm font-medium">Accent</p>
                          <p className="text-xs text-muted-foreground">#FF9500</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="generation-panel">
                <CardHeader>
                  <CardTitle>Typography</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-2xl font-bold mb-2">Primary Font</h3>
                      <p className="text-muted-foreground">Inter - Headlines & Body</p>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold mb-1">Secondary Font</h4>
                      <p className="text-muted-foreground">Roboto Mono - Code & Technical</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="generation-panel lg:col-span-2">
                <CardHeader>
                  <CardTitle>Logo Usage Guidelines</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center space-y-2">
                      <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                        <div className="w-16 h-16 bg-primary rounded-full"></div>
                      </div>
                      <p className="text-sm font-medium">Minimum Size</p>
                      <p className="text-xs text-muted-foreground">32px / 0.5 inch</p>
                    </div>
                    <div className="text-center space-y-2">
                      <div className="aspect-square bg-muted rounded-lg flex items-center justify-center">
                        <div className="w-20 h-20 border-2 border-primary rounded-full flex items-center justify-center">
                          <div className="w-12 h-12 bg-primary rounded-full"></div>
                        </div>
                      </div>
                      <p className="text-sm font-medium">Clear Space</p>
                      <p className="text-xs text-muted-foreground">0.5x logo height</p>
                    </div>
                    <div className="text-center space-y-2">
                      <div className="aspect-square bg-gradient-to-br from-background to-muted rounded-lg flex items-center justify-center">
                        <div className="w-16 h-16 bg-primary rounded-full opacity-80"></div>
                      </div>
                      <p className="text-sm font-medium">Backgrounds</p>
                      <p className="text-xs text-muted-foreground">Light & Dark approved</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default BrandStudio;