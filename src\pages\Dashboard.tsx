import { useState } from "react";
import { Plus, Sparkles, Palette, Layout, TrendingUp } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";

const Dashboard = () => {
  const navigate = useNavigate();
  const [projects] = useState([
    {
      id: "1",
      name: "TechFlow Logo",
      description: "Modern tech startup branding",
      lastModified: "2 hours ago",
      status: "In Progress",
      preview: null
    },
    {
      id: "2", 
      name: "Coffee Bean Co",
      description: "Artisan coffee shop identity",
      lastModified: "1 day ago",
      status: "Completed",
      preview: null
    }
  ]);

  const features = [
    {
      icon: Sparkles,
      title: "AI-Powered Generation",
      description: "Create unique logos with advanced AI models",
      color: "text-primary"
    },
    {
      icon: Palette,
      title: "Professional Editor",
      description: "Fine-tune with professional vector tools",
      color: "text-secondary"
    },
    {
      icon: Layout,
      title: "Brand Ecosystem",
      description: "Generate complete brand identity packages",
      color: "text-accent"
    },
    {
      icon: TrendingUp,
      title: "Analytics Insights",
      description: "Track performance and brand impact",
      color: "text-primary"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-6">
        <div className="absolute inset-0 bg-gradient-hero opacity-10" />
        <div className="relative max-w-7xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 bg-card/50 backdrop-blur-sm px-4 py-2 rounded-full border border-border/50 mb-6">
            <Sparkles className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium">Powered by Advanced AI</span>
          </div>
          
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-hero bg-clip-text text-transparent">
            LogoCraft AI
          </h1>
          
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            Create professional brand identities with AI-powered logo generation, 
            vector editing, and complete brand ecosystem management.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="btn-hero"
              onClick={() => navigate("/editor")}
            >
              <Plus className="w-5 h-5 mr-2" />
              Create New Logo
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="border-border/50 hover:bg-card/50 backdrop-blur-sm"
            >
              View Gallery
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Professional Brand Creation
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Everything you need to build and manage your brand identity in one powerful platform.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="generation-panel hover:shadow-glow transition-smooth">
                <CardHeader>
                  <feature.icon className={`w-8 h-8 ${feature.color} mb-2`} />
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Recent Projects */}
      <section className="py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl md:text-3xl font-bold mb-2">Recent Projects</h2>
              <p className="text-muted-foreground">Continue working on your brand identities</p>
            </div>
            <Button 
              onClick={() => navigate("/editor")}
              className="bg-gradient-primary hover:shadow-glow transition-smooth"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Project
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {projects.map((project) => (
              <Card 
                key={project.id} 
                className="generation-panel hover:shadow-glow transition-smooth cursor-pointer"
                onClick={() => navigate(`/editor/${project.id}`)}
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{project.name}</CardTitle>
                    <Badge 
                      variant={project.status === "Completed" ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {project.status}
                    </Badge>
                  </div>
                  <CardDescription>{project.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="aspect-video bg-muted rounded-lg mb-4 flex items-center justify-center">
                    <Palette className="w-8 h-8 text-muted-foreground" />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Last modified {project.lastModified}
                  </p>
                </CardContent>
              </Card>
            ))}
            
            {/* Create New Project Card */}
            <Card 
              className="generation-panel border-dashed border-2 hover:shadow-glow transition-smooth cursor-pointer"
              onClick={() => navigate("/editor")}
            >
              <CardContent className="flex flex-col items-center justify-center h-full min-h-[300px]">
                <Plus className="w-12 h-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">Create New Project</h3>
                <p className="text-muted-foreground text-center">
                  Start building your next brand identity with AI
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Dashboard;