import { useState, useRef, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { 
  ArrowLeft, 
  Download, 
  Palette, 
  Type, 
  Layers, 
  Sparkles,
  Save,
  Share,
  Wand2,
  AlertCircle
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { APIKeyManager } from "@/components/APIKeyManager";
import { aiService } from "@/services/aiService";
import { toast } from "sonner";

const ProjectEditor = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  const [projectName, setProjectName] = useState(projectId ? "TechFlow Logo" : "New Project");
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedLogos, setGeneratedLogos] = useState<string[]>([]);
  const [selectedLogo, setSelectedLogo] = useState<string | null>(null);
  
  // AI Generation parameters
  const [style, setStyle] = useState("modern");
  const [complexity, setComplexity] = useState([50]);
  const [colorScheme, setColorScheme] = useState("colorful");

  const styles = [
    { value: "modern", label: "Modern", color: "bg-gradient-primary" },
    { value: "minimal", label: "Minimal", color: "bg-gradient-secondary" },
    { value: "vintage", label: "Vintage", color: "bg-gradient-accent" },
    { value: "playful", label: "Playful", color: "bg-gradient-hero" },
    { value: "elegant", label: "Elegant", color: "bg-card" },
    { value: "bold", label: "Bold", color: "bg-primary" }
  ];

  const colorSchemes = [
    { value: "colorful", label: "Colorful" },
    { value: "monochrome", label: "Monochrome" },
    { value: "warm", label: "Warm Tones" },
    { value: "cool", label: "Cool Tones" },
    { value: "earth", label: "Earth Tones" }
  ];

  useEffect(() => {
    // Initialize canvas
    if (canvasRef.current) {
      const ctx = canvasRef.current.getContext('2d');
      if (ctx) {
        ctx.fillStyle = '#1a1a1a';
        ctx.fillRect(0, 0, 800, 600);
        ctx.fillStyle = '#666';
        ctx.font = '24px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('Your logo will appear here', 400, 300);
      }
    }
  }, []);

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a description for your logo");
      return;
    }

    // Check if any API keys are configured
    const hasApiKeys = aiService.hasApiKey('huggingface') || 
                      aiService.hasApiKey('replicate') || 
                      aiService.hasApiKey('openai');
    
    if (!hasApiKeys) {
      toast.error("Please configure at least one AI API key to generate logos");
      return;
    }

    setIsGenerating(true);
    try {
      const request = {
        prompt,
        style,
        colorScheme: colorScheme as any,
        complexity: complexity[0],
      };

      const response = await aiService.generateLogo(request);
      
      if (response.success && response.images.length > 0) {
        setGeneratedLogos(response.images.map(img => img.url));
        setSelectedLogo(response.images[0].url);
        toast.success(`Generated ${response.images.length} logo variation${response.images.length > 1 ? 's' : ''}!`);
      } else {
        toast.error("Failed to generate logo. Please try different settings or check your API keys.");
      }
      
    } catch (error) {
      console.error('Generation error:', error);
      toast.error("Failed to generate logo. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSave = () => {
    toast.success("Project saved successfully!");
  };

  const handleExport = () => {
    toast.success("Logo exported successfully!");
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border/50 bg-card/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate("/")}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
              <div className="h-6 w-px bg-border" />
              <Input
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
                className="font-semibold text-lg border-none bg-transparent px-0 focus-visible:ring-0"
              />
            </div>
            
            <div className="flex items-center gap-2">
              <APIKeyManager />
              <Button variant="outline" size="sm" onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                Save
              </Button>
              <Button variant="outline" size="sm">
                <Share className="w-4 h-4 mr-2" />
                Share
              </Button>
              <Button size="sm" className="bg-gradient-primary" onClick={handleExport}>
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* AI Generation Panel */}
          <div className="lg:col-span-1">
            <Card className="generation-panel">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5 text-primary" />
                  AI Generation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="prompt">Describe your logo</Label>
                  <Textarea
                    id="prompt"
                    placeholder="A modern tech startup logo with clean lines and geometric shapes..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    className="mt-2"
                    rows={4}
                  />
                </div>

                <div>
                  <Label>Style</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {styles.map((s) => (
                      <Badge
                        key={s.value}
                        variant={style === s.value ? "default" : "outline"}
                        className={`cursor-pointer justify-center p-2 ${
                          style === s.value ? s.color : ""
                        }`}
                        onClick={() => setStyle(s.value)}
                      >
                        {s.label}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <Label>Color Scheme</Label>
                  <div className="grid grid-cols-1 gap-2 mt-2">
                    {colorSchemes.map((scheme) => (
                      <Badge
                        key={scheme.value}
                        variant={colorScheme === scheme.value ? "default" : "outline"}
                        className="cursor-pointer justify-center p-2"
                        onClick={() => setColorScheme(scheme.value)}
                      >
                        {scheme.label}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <Label>Complexity: {complexity[0]}%</Label>
                  <Slider
                    value={complexity}
                    onValueChange={setComplexity}
                    max={100}
                    step={10}
                    className="mt-2"
                  />
                </div>

                <Button 
                  onClick={handleGenerate}
                  disabled={isGenerating}
                  className="w-full bg-gradient-primary hover:shadow-glow transition-smooth"
                >
                  {isGenerating ? (
                    <>
                      <Wand2 className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      Generate Logo
                    </>
                  )}
                </Button>

                {generatedLogos.length > 0 && (
                  <div>
                    <Label>Generated Variations</Label>
                    <div className="grid grid-cols-3 gap-2 mt-2">
                      {generatedLogos.map((logo, index) => (
                        <div
                          key={index}
                          className={`aspect-square border-2 rounded-lg cursor-pointer transition-smooth ${
                            selectedLogo === logo 
                              ? "border-primary shadow-glow" 
                              : "border-border hover:border-primary/50"
                          }`}
                          onClick={() => setSelectedLogo(logo)}
                        >
                          <img
                            src={logo}
                            alt={`Logo variation ${index + 1}`}
                            className="w-full h-full object-contain rounded-lg"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Main Canvas Area */}
          <div className="lg:col-span-2">
            <Card className="vector-canvas">
              <CardContent className="p-6">
                <div className="aspect-[4/3] bg-muted rounded-xl flex items-center justify-center relative overflow-hidden">
                  {selectedLogo ? (
                    <img
                      src={selectedLogo}
                      alt="Selected logo"
                      className="max-w-full max-h-full object-contain"
                    />
                  ) : (
                    <div className="text-center">
                      <Palette className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                      <p className="text-lg font-medium text-muted-foreground">
                        Your logo canvas
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Generate or upload a logo to start editing
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tools Panel */}
          <div className="lg:col-span-1">
            <Tabs defaultValue="design" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="design">
                  <Palette className="w-4 h-4" />
                </TabsTrigger>
                <TabsTrigger value="text">
                  <Type className="w-4 h-4" />
                </TabsTrigger>
                <TabsTrigger value="layers">
                  <Layers className="w-4 h-4" />
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="design" className="mt-4">
                <Card className="generation-panel">
                  <CardHeader>
                    <CardTitle className="text-lg">Design Tools</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label>Colors</Label>
                      <div className="grid grid-cols-4 gap-2 mt-2">
                        {['#6366f1', '#0f72ff', '#ff9500', '#10b981', '#ef4444', '#8b5cf6'].map((color) => (
                          <div
                            key={color}
                            className="aspect-square rounded-lg border-2 border-border cursor-pointer hover:border-primary transition-smooth"
                            style={{ backgroundColor: color }}
                          />
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <Label>Stroke Width</Label>
                      <Slider
                        defaultValue={[2]}
                        max={10}
                        step={1}
                        className="mt-2"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="text" className="mt-4">
                <Card className="generation-panel">
                  <CardHeader>
                    <CardTitle className="text-lg">Typography</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label>Font Family</Label>
                      <select className="w-full mt-2 p-2 rounded-lg border border-border bg-background">
                        <option>Inter</option>
                        <option>Roboto</option>
                        <option>Poppins</option>
                        <option>Montserrat</option>
                      </select>
                    </div>
                    
                    <div>
                      <Label>Font Size</Label>
                      <Slider
                        defaultValue={[24]}
                        max={72}
                        min={8}
                        step={2}
                        className="mt-2"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="layers" className="mt-4">
                <Card className="generation-panel">
                  <CardHeader>
                    <CardTitle className="text-lg">Layers</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="p-3 bg-muted rounded-lg flex items-center justify-between">
                        <span className="text-sm">Background</span>
                        <div className="w-4 h-4 bg-background rounded border" />
                      </div>
                      <div className="p-3 bg-card rounded-lg border border-primary flex items-center justify-between">
                        <span className="text-sm font-medium">Logo</span>
                        <div className="w-4 h-4 bg-primary rounded" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectEditor;