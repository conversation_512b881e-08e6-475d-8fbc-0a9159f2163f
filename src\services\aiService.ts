import { AIGenerationRequest, AIGenerationResponse, GeneratedImage } from '@/types/ai';

interface APIKeys {
  huggingface?: string;
  openai?: string;
  replicate?: string;
}

class AIService {
  private getStoredApiKeys(): APIKeys {
    const stored = localStorage.getItem('logocraft_api_keys');
    return stored ? JSON.parse(stored) : {};
  }

  private storeApiKeys(keys: APIKeys): void {
    localStorage.setItem('logocraft_api_keys', JSON.stringify(keys));
  }

  setApiKey(provider: keyof APIKeys, key: string): void {
    const keys = this.getStoredApiKeys();
    keys[provider] = key;
    this.storeApiKeys(keys);
  }

  getApiKey(provider: keyof APIKeys): string | undefined {
    const keys = this.getStoredApiKeys();

    // First check localStorage
    if (keys[provider]) {
      console.log(`Found ${provider} API key in localStorage`);
      return keys[provider];
    }

    // Fallback to environment variables
    let envKey: string | undefined;
    switch (provider) {
      case 'huggingface':
        envKey = import.meta.env.VITE_HUGGINGFACE_API_KEY;
        break;
      case 'openai':
        envKey = import.meta.env.VITE_OPENAI_API_KEY;
        break;
      case 'replicate':
        envKey = import.meta.env.VITE_REPLICATE_API_TOKEN;
        break;
      default:
        envKey = undefined;
    }

    if (envKey) {
      console.log(`Found ${provider} API key in environment variables`);
    } else {
      console.log(`No ${provider} API key found in localStorage or environment variables`);
    }

    return envKey;
  }

  hasApiKey(provider: keyof APIKeys): boolean {
    const apiKey = this.getApiKey(provider);
    console.log(`Checking API key for ${provider}:`, apiKey ? 'Found' : 'Not found');
    return !!apiKey;
  }

  async generateLogo(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    const startTime = Date.now();
    
    try {
      // Try multiple generation methods
      const methods = [
        () => this.generateWithHuggingFace(request),
        () => this.generateWithReplicate(request),
        () => this.generateWithOpenAI(request),
      ];

      for (const method of methods) {
        try {
          const result = await method();
          if (result.success) {
            return {
              ...result,
              processingTime: Date.now() - startTime,
            };
          }
        } catch (error) {
          console.warn('Generation method failed, trying next...', error);
          continue;
        }
      }

      throw new Error('All generation methods failed');
    } catch (error) {
      console.error('AI Generation failed:', error);
      return {
        success: false,
        images: [],
        processingTime: Date.now() - startTime,
        modelUsed: 'failed',
      };
    }
  }

  private async generateWithHuggingFace(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    const apiKey = this.getApiKey('huggingface');
    if (!apiKey) {
      throw new Error('Hugging Face API key not configured');
    }

    const models = [
      'stabilityai/stable-diffusion-xl-base-1.0',
      'runwayml/stable-diffusion-v1-5',
      'prompthero/openjourney-v4',
    ];

    for (const model of models) {
      try {
        const response = await fetch(`https://api-inference.huggingface.co/models/${model}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            inputs: this.enhancePrompt(request),
            parameters: {
              num_inference_steps: 25,
              guidance_scale: 7.5,
              width: 512,
              height: 512,
            },
          }),
        });

        if (response.ok) {
          const blob = await response.blob();
          const imageUrl = URL.createObjectURL(blob);
          
          return {
            success: true,
            images: [{
              id: crypto.randomUUID(),
              url: imageUrl,
              format: 'png',
              width: 512,
              height: 512,
              metadata: {
                prompt: request.prompt,
                style: request.style,
                model: model,
              },
            }],
            processingTime: 0,
            modelUsed: model,
          };
        }
      } catch (error) {
        console.warn(`Model ${model} failed:`, error);
        continue;
      }
    }

    throw new Error('All Hugging Face models failed');
  }

  private async generateWithReplicate(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    const apiKey = this.getApiKey('replicate');
    if (!apiKey) {
      throw new Error('Replicate API key not configured');
    }

    const response = await fetch('https://api.replicate.com/v1/predictions', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        version: 'ac732df83cea7fff18b8472768c88ad041fa750ff7682a21affe81863cbe77e4',
        input: {
          prompt: this.enhancePrompt(request),
          width: 512,
          height: 512,
          num_inference_steps: 25,
          guidance_scale: 7.5,
          num_outputs: 1,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Replicate API error: ${response.statusText}`);
    }

    const prediction = await response.json();
    
    // Poll for completion
    let result = prediction;
    while (result.status === 'starting' || result.status === 'processing') {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const pollResponse = await fetch(`https://api.replicate.com/v1/predictions/${result.id}`, {
        headers: { 'Authorization': `Token ${apiKey}` },
      });
      result = await pollResponse.json();
    }

    if (result.status === 'succeeded' && result.output?.length > 0) {
      return {
        success: true,
        images: [{
          id: crypto.randomUUID(),
          url: result.output[0],
          format: 'png',
          width: 512,
          height: 512,
          metadata: {
            prompt: request.prompt,
            style: request.style,
            model: 'stable-diffusion',
          },
        }],
        processingTime: 0,
        modelUsed: 'replicate-stable-diffusion',
      };
    }

    throw new Error('Replicate generation failed');
  }

  private async generateWithOpenAI(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    const apiKey = this.getApiKey('openai');
    if (!apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await fetch('https://api.openai.com/v1/images/generations', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'dall-e-3',
        prompt: this.enhancePrompt(request),
        size: '1024x1024',
        quality: 'standard',
        n: 1,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.data && result.data.length > 0) {
      return {
        success: true,
        images: [{
          id: crypto.randomUUID(),
          url: result.data[0].url,
          format: 'png',
          width: 1024,
          height: 1024,
          metadata: {
            prompt: request.prompt,
            style: request.style,
            model: 'dall-e-3',
          },
        }],
        processingTime: 0,
        modelUsed: 'dall-e-3',
      };
    }

    throw new Error('OpenAI generation failed');
  }

  private enhancePrompt(request: AIGenerationRequest): string {
    const stylePrompts = {
      modern: 'clean, minimalist, geometric, professional',
      vintage: 'retro, classic, ornate, traditional',
      minimal: 'simple, clean lines, negative space',
      bold: 'strong, impactful, high contrast',
      elegant: 'sophisticated, refined, luxurious',
      playful: 'fun, colorful, creative, whimsical',
    };

    const colorPrompts = {
      colorful: 'vibrant colors, full spectrum',
      monochrome: 'black and white, grayscale',
      warm: 'warm tones, reds, oranges, yellows',
      cool: 'cool tones, blues, greens, purples',
      earth: 'earth tones, browns, beiges, natural colors',
    };

    return `professional logo design, ${request.prompt}, ${stylePrompts[request.style as keyof typeof stylePrompts]}, ${colorPrompts[request.colorScheme]}, vector graphics, high quality, scalable, brand identity, corporate branding`;
  }
}

export const aiService = new AIService();