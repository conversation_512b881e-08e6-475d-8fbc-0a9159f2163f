import { AIGenerationRequest, AIGenerationResponse, GeneratedImage } from '@/types/ai';

interface APIKeys {
  huggingface?: string;
  openai?: string;
  replicate?: string;
}

class AIService {
  private getStoredApiKeys(): APIKeys {
    const stored = localStorage.getItem('logocraft_api_keys');
    return stored ? JSON.parse(stored) : {};
  }

  private storeApiKeys(keys: APIKeys): void {
    localStorage.setItem('logocraft_api_keys', JSON.stringify(keys));
  }

  setApiKey(provider: keyof APIKeys, key: string): void {
    const keys = this.getStoredApiKeys();
    keys[provider] = key;
    this.storeApiKeys(keys);
  }

  getApiKey(provider: keyof APIKeys): string | undefined {
    const keys = this.getStoredApiKeys();

    // First check localStorage
    if (keys[provider]) {
      return keys[provider];
    }

    // Fallback to environment variables
    let envKey: string | undefined;
    switch (provider) {
      case 'huggingface':
        envKey = import.meta.env.VITE_HUGGINGFACE_API_KEY;
        break;
      case 'openai':
        envKey = import.meta.env.VITE_OPENAI_API_KEY;
        break;
      case 'replicate':
        envKey = import.meta.env.VITE_REPLICATE_API_TOKEN;
        break;
      default:
        envKey = undefined;
    }

    // Return the environment key if found

    return envKey;
  }

  hasApiKey(provider: keyof APIKeys): boolean {
    const apiKey = this.getApiKey(provider);
    return !!apiKey;
  }

  async generateLogo(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    const startTime = Date.now();
    
    try {
      // Try multiple generation methods
      const methods = [
        () => this.generateWithHuggingFace(request),
        () => this.generateWithReplicate(request),
        () => this.generateWithOpenAI(request),
      ];

      for (const method of methods) {
        try {
          const result = await method();
          if (result.success) {
            return {
              ...result,
              processingTime: Date.now() - startTime,
            };
          }
        } catch (error) {
          console.warn('Generation method failed, trying next...', error);
          continue;
        }
      }

      throw new Error('All generation methods failed');
    } catch (error) {
      console.error('AI Generation failed:', error);
      return {
        success: false,
        images: [],
        processingTime: Date.now() - startTime,
        modelUsed: 'failed',
      };
    }
  }

  private async generateWithHuggingFace(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    const apiKey = this.getApiKey('huggingface');
    if (!apiKey) {
      throw new Error('Hugging Face API key not configured');
    }

    // Get intelligently ordered models based on request characteristics
    const models = this.getOptimalModelOrder(request);

    for (const model of models) {
      try {
        // Get optimized parameters for each model type
        const modelParams = this.getOptimizedParameters(model, request);

        // Try with retry logic for transient failures
        const result = await this.tryModelWithRetry(model, request, modelParams, apiKey);

        if (result.success) {
          return result;
        }

      } catch (error) {
        console.warn(`Model ${model} failed:`, error);
        continue;
      }
    }

    throw new Error('All Hugging Face models failed');
  }

  private async generateWithReplicate(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    const apiKey = this.getApiKey('replicate');
    if (!apiKey) {
      throw new Error('Replicate API key not configured');
    }

    const response = await fetch('https://api.replicate.com/v1/predictions', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        version: 'ac732df83cea7fff18b8472768c88ad041fa750ff7682a21affe81863cbe77e4',
        input: {
          prompt: this.enhancePrompt(request),
          width: 512,
          height: 512,
          num_inference_steps: 25,
          guidance_scale: 7.5,
          num_outputs: 1,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Replicate API error: ${response.statusText}`);
    }

    const prediction = await response.json();
    
    // Poll for completion
    let result = prediction;
    while (result.status === 'starting' || result.status === 'processing') {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const pollResponse = await fetch(`https://api.replicate.com/v1/predictions/${result.id}`, {
        headers: { 'Authorization': `Token ${apiKey}` },
      });
      result = await pollResponse.json();
    }

    if (result.status === 'succeeded' && result.output?.length > 0) {
      return {
        success: true,
        images: [{
          id: crypto.randomUUID(),
          url: result.output[0],
          format: 'png',
          width: 512,
          height: 512,
          metadata: {
            prompt: request.prompt,
            style: request.style,
            model: 'stable-diffusion',
          },
        }],
        processingTime: 0,
        modelUsed: 'replicate-stable-diffusion',
      };
    }

    throw new Error('Replicate generation failed');
  }

  private async generateWithOpenAI(request: AIGenerationRequest): Promise<AIGenerationResponse> {
    const apiKey = this.getApiKey('openai');
    if (!apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await fetch('https://api.openai.com/v1/images/generations', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'dall-e-3',
        prompt: this.enhancePrompt(request),
        size: '1024x1024',
        quality: 'standard',
        n: 1,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const result = await response.json();
    
    if (result.data && result.data.length > 0) {
      return {
        success: true,
        images: [{
          id: crypto.randomUUID(),
          url: result.data[0].url,
          format: 'png',
          width: 1024,
          height: 1024,
          metadata: {
            prompt: request.prompt,
            style: request.style,
            model: 'dall-e-3',
          },
        }],
        processingTime: 0,
        modelUsed: 'dall-e-3',
      };
    }

    throw new Error('OpenAI generation failed');
  }

  private async tryModelWithRetry(
    model: string,
    request: AIGenerationRequest,
    modelParams: any,
    apiKey: string,
    maxRetries: number = 2
  ): Promise<AIGenerationResponse> {
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          // Exponential backoff: wait 2^attempt seconds
          const delay = Math.pow(2, attempt) * 1000;
          console.log(`Retrying ${model} in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        const response = await fetch(`https://api-inference.huggingface.co/models/${model}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            inputs: this.enhancePrompt(request),
            parameters: modelParams,
          }),
        });

        if (response.ok) {
          const blob = await response.blob();

          // Check if we got a valid image (not an error message)
          if (blob.size < 1000) {
            throw new Error('Received suspiciously small response, likely an error');
          }

          // Apply post-processing to enhance the logo
          const enhancedBlob = await this.postProcessImage(blob, request);
          const imageUrl = URL.createObjectURL(enhancedBlob);

          return {
            success: true,
            images: [{
              id: crypto.randomUUID(),
              url: imageUrl,
              format: 'png',
              width: modelParams.width,
              height: modelParams.height,
              metadata: {
                prompt: request.prompt,
                style: request.style,
                model: model,
                parameters: modelParams,
                enhanced: true,
                attempts: attempt + 1,
              },
            }],
            processingTime: 0,
            modelUsed: model,
          };
        } else if (response.status === 503 && attempt < maxRetries) {
          // Model is loading, retry
          console.log(`Model ${model} is loading, will retry...`);
          continue;
        } else {
          const errorText = await response.text();
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }
        console.warn(`Attempt ${attempt + 1} failed for ${model}:`, error);
      }
    }

    return { success: false, images: [], processingTime: 0, modelUsed: model };
  }

  private getOptimalModelOrder(request: AIGenerationRequest): string[] {
    // Define model capabilities and characteristics
    const modelProfiles = {
      'Shakker-Labs/FLUX.1-dev-LoRA-Logo-Design': {
        logoSpecialized: true,
        quality: 9,
        speed: 7,
        textRendering: 8,
        styles: ['minimal', 'modern', 'bold'],
        complexity: [1, 10], // Works well for all complexity levels
      },
      'stabilityai/stable-diffusion-3.5-medium': {
        logoSpecialized: false,
        quality: 9,
        speed: 6,
        textRendering: 7,
        styles: ['modern', 'elegant', 'bold'],
        complexity: [5, 10],
      },
      'black-forest-labs/FLUX.1-schnell': {
        logoSpecialized: false,
        quality: 8,
        speed: 9,
        textRendering: 7,
        styles: ['modern', 'minimal', 'playful'],
        complexity: [3, 8],
      },
      'stabilityai/stable-diffusion-xl-base-1.0': {
        logoSpecialized: false,
        quality: 8,
        speed: 7,
        textRendering: 6,
        styles: ['modern', 'elegant', 'vintage'],
        complexity: [4, 9],
      },
      'runwayml/stable-diffusion-v1-5': {
        logoSpecialized: false,
        quality: 7,
        speed: 8,
        textRendering: 5,
        styles: ['vintage', 'playful', 'bold'],
        complexity: [2, 7],
      },
      'prompthero/openjourney-v4': {
        logoSpecialized: false,
        quality: 7,
        speed: 8,
        textRendering: 6,
        styles: ['playful', 'vintage', 'bold'],
        complexity: [3, 8],
      },
      'stabilityai/stable-diffusion-2-1': {
        logoSpecialized: false,
        quality: 6,
        speed: 8,
        textRendering: 5,
        styles: ['modern', 'minimal'],
        complexity: [2, 6],
      },
    };

    // Score each model based on request characteristics
    const scoredModels = Object.entries(modelProfiles).map(([model, profile]) => {
      let score = 0;

      // Bonus for logo specialization
      if (profile.logoSpecialized) score += 30;

      // Style compatibility
      if (profile.styles.includes(request.style)) score += 20;

      // Complexity compatibility
      const [minComp, maxComp] = profile.complexity;
      if (request.complexity >= minComp && request.complexity <= maxComp) {
        score += 15;
      } else {
        // Penalty for complexity mismatch
        const distance = Math.min(
          Math.abs(request.complexity - minComp),
          Math.abs(request.complexity - maxComp)
        );
        score -= distance * 2;
      }

      // Base quality score
      score += profile.quality * 2;

      // Speed bonus (for user experience)
      score += profile.speed;

      // Text rendering bonus (important for logos)
      score += profile.textRendering;

      return { model, score, profile };
    });

    // Sort by score (highest first) and return model names
    return scoredModels
      .sort((a, b) => b.score - a.score)
      .map(item => {
        console.log(`Model ${item.model}: score ${item.score}`);
        return item.model;
      });
  }

  private async postProcessImage(blob: Blob, request: AIGenerationRequest): Promise<Blob> {
    try {
      // Create canvas for image processing
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) return blob;

      // Load the image
      const img = new Image();
      const imageUrl = URL.createObjectURL(blob);

      return new Promise((resolve) => {
        img.onload = () => {
          canvas.width = img.width;
          canvas.height = img.height;

          // Draw the original image
          ctx.drawImage(img, 0, 0);

          // Get image data for processing
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const data = imageData.data;

          // Apply enhancements based on style and requirements
          this.enhanceImageData(data, request);

          // Put the enhanced data back
          ctx.putImageData(imageData, 0, 0);

          // Convert back to blob
          canvas.toBlob((enhancedBlob) => {
            URL.revokeObjectURL(imageUrl);
            resolve(enhancedBlob || blob);
          }, 'image/png', 0.95);
        };

        img.onerror = () => {
          URL.revokeObjectURL(imageUrl);
          resolve(blob); // Return original if processing fails
        };

        img.src = imageUrl;
      });
    } catch (error) {
      console.warn('Post-processing failed, returning original image:', error);
      return blob;
    }
  }

  private enhanceImageData(data: Uint8ClampedArray, request: AIGenerationRequest) {
    // Apply different enhancements based on style
    switch (request.style) {
      case 'minimal':
        this.increaseContrast(data, 1.2);
        this.adjustBrightness(data, 5);
        break;
      case 'bold':
        this.increaseContrast(data, 1.4);
        this.increaseSaturation(data, 1.3);
        break;
      case 'elegant':
        this.adjustBrightness(data, 10);
        this.increaseContrast(data, 1.1);
        break;
      case 'modern':
        this.increaseContrast(data, 1.15);
        this.sharpenImage(data);
        break;
      default:
        this.increaseContrast(data, 1.1);
        this.adjustBrightness(data, 5);
    }
  }

  private increaseContrast(data: Uint8ClampedArray, factor: number) {
    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.min(255, Math.max(0, (data[i] - 128) * factor + 128));     // Red
      data[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * factor + 128)); // Green
      data[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * factor + 128)); // Blue
    }
  }

  private adjustBrightness(data: Uint8ClampedArray, adjustment: number) {
    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.min(255, Math.max(0, data[i] + adjustment));     // Red
      data[i + 1] = Math.min(255, Math.max(0, data[i + 1] + adjustment)); // Green
      data[i + 2] = Math.min(255, Math.max(0, data[i + 2] + adjustment)); // Blue
    }
  }

  private increaseSaturation(data: Uint8ClampedArray, factor: number) {
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];

      // Convert to HSL, increase saturation, convert back
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      const diff = max - min;

      if (diff !== 0) {
        const sum = max + min;
        const lightness = sum / 2;
        const saturation = lightness > 127 ? diff / (510 - sum) : diff / sum;
        const newSaturation = Math.min(1, saturation * factor);

        // Apply the enhanced saturation (simplified)
        const enhancement = (newSaturation - saturation) * 0.5;
        data[i] = Math.min(255, Math.max(0, r + (r - lightness) * enhancement));
        data[i + 1] = Math.min(255, Math.max(0, g + (g - lightness) * enhancement));
        data[i + 2] = Math.min(255, Math.max(0, b + (b - lightness) * enhancement));
      }
    }
  }

  private sharpenImage(data: Uint8ClampedArray) {
    // Simple sharpening filter - enhance edges
    const factor = 0.1;
    for (let i = 0; i < data.length; i += 4) {
      if (i > 4 && i < data.length - 4) {
        const current = (data[i] + data[i + 1] + data[i + 2]) / 3;
        const prev = (data[i - 4] + data[i - 3] + data[i - 2]) / 3;
        const next = (data[i + 4] + data[i + 5] + data[i + 6]) / 3;

        const edge = Math.abs(current - prev) + Math.abs(current - next);
        const enhancement = edge * factor;

        data[i] = Math.min(255, Math.max(0, data[i] + enhancement));
        data[i + 1] = Math.min(255, Math.max(0, data[i + 1] + enhancement));
        data[i + 2] = Math.min(255, Math.max(0, data[i + 2] + enhancement));
      }
    }
  }

  private getOptimizedParameters(model: string, request: AIGenerationRequest) {
    // Base parameters optimized for logo generation
    const baseParams = {
      num_inference_steps: 30, // Increased for better quality
      guidance_scale: 8.0, // Higher guidance for more controlled generation
      width: 1024, // Higher resolution for better logo quality
      height: 1024,
    };

    // Model-specific optimizations
    if (model.includes('stable-diffusion-3.5')) {
      return {
        ...baseParams,
        num_inference_steps: 35, // SD 3.5 benefits from more steps
        guidance_scale: 7.0, // SD 3.5 works better with slightly lower guidance
        width: 1024,
        height: 1024,
      };
    }

    if (model.includes('FLUX')) {
      return {
        ...baseParams,
        num_inference_steps: 24, // FLUX is optimized for fewer steps
        guidance_scale: 3.5, // FLUX uses lower guidance scale
        width: 1024,
        height: 1024,
      };
    }

    if (model.includes('stable-diffusion-xl')) {
      return {
        ...baseParams,
        num_inference_steps: 30,
        guidance_scale: 8.5, // SDXL benefits from higher guidance
        width: 1024,
        height: 1024,
      };
    }

    if (model.includes('stable-diffusion-v1-5') || model.includes('stable-diffusion-2')) {
      return {
        ...baseParams,
        num_inference_steps: 35, // Older models need more steps
        guidance_scale: 9.0, // Higher guidance for better control
        width: 768, // Lower resolution for older models to maintain speed
        height: 768,
      };
    }

    // Default parameters for other models
    return baseParams;
  }

  private enhancePrompt(request: AIGenerationRequest): string {
    const stylePrompts = {
      modern: 'clean minimalist design, geometric shapes, contemporary typography, sleek lines, professional corporate identity, flat design, sans-serif elements',
      vintage: 'retro classic design, ornate decorative elements, traditional craftsmanship, heritage brand, vintage typography, timeless elegance, artisanal quality',
      minimal: 'ultra-minimalist, negative space utilization, simple geometric forms, clean typography, monoline design, essential elements only, sophisticated simplicity',
      bold: 'strong visual impact, high contrast design, powerful typography, dynamic composition, attention-grabbing, confident branding, striking visual presence',
      elegant: 'sophisticated luxury design, refined typography, premium brand identity, graceful curves, upscale aesthetic, polished finish, high-end appeal',
      playful: 'creative whimsical design, vibrant personality, friendly approachable, fun brand character, colorful expression, engaging visual elements, youthful energy',
    };

    const colorPrompts = {
      colorful: 'vibrant color palette, full spectrum harmony, energetic brand colors, dynamic color composition, rich saturated tones',
      monochrome: 'black and white design, grayscale palette, high contrast monochrome, sophisticated single-color, timeless black white aesthetic',
      warm: 'warm color temperature, reds oranges yellows, inviting friendly palette, energetic warm tones, cozy brand feeling',
      cool: 'cool color temperature, blues greens purples, calming professional palette, trustworthy cool tones, serene brand aesthetic',
      earth: 'natural earth tones, browns beiges greens, organic color palette, sustainable brand colors, grounded natural aesthetic',
    };

    // Core logo design keywords
    const logoCore = 'professional logo design, brand identity, corporate branding, scalable vector graphics';

    // Quality and technical specifications
    const qualitySpecs = 'high resolution, crisp clean lines, perfect symmetry, balanced composition, professional quality';

    // Logo-specific enhancements based on complexity
    const complexityEnhancements = request.complexity > 7
      ? 'detailed intricate design, sophisticated elements, layered composition, rich visual hierarchy'
      : request.complexity > 4
      ? 'balanced detail level, clear focal points, structured composition, readable at small sizes'
      : 'simple iconic design, instantly recognizable, memorable symbol, minimal elements, maximum impact';

    // Negative prompts to avoid common issues
    const negativePrompts = 'no blurry edges, no pixelation, no distorted text, no cluttered composition, no amateur design';

    // Style-specific prompt
    const stylePrompt = stylePrompts[request.style as keyof typeof stylePrompts] || stylePrompts.modern;

    // Color-specific prompt
    const colorPrompt = colorPrompts[request.colorScheme] || colorPrompts.colorful;

    // Construct the enhanced prompt
    const enhancedPrompt = [
      logoCore,
      request.prompt,
      stylePrompt,
      colorPrompt,
      complexityEnhancements,
      qualitySpecs,
      'masterpiece, best quality, ultra-detailed',
      `negative prompt: ${negativePrompts}`
    ].join(', ');

    return enhancedPrompt;
  }
}

export const aiService = new AIService();