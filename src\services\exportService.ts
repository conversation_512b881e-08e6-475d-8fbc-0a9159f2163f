import { VectorDocument } from './vectorService';

export interface ExportOptions {
  format: 'svg' | 'png' | 'jpg' | 'pdf' | 'eps';
  width?: number;
  height?: number;
  scale: number;
  quality: number;
  transparent: boolean;
  dpi: number;
  colorMode: 'rgb' | 'cmyk';
}

export interface ExportResult {
  success: boolean;
  blob?: Blob;
  url?: string;
  filename: string;
  error?: string;
}

class ExportService {
  async exportDocument(
    document: VectorDocument, 
    options: ExportOptions
  ): Promise<ExportResult> {
    const filename = `logo_${Date.now()}.${options.format}`;
    
    try {
      switch (options.format) {
        case 'svg':
          return await this.exportSVG(document, options, filename);
        case 'png':
          return await this.exportPNG(document, options, filename);
        case 'jpg':
          return await this.exportJPG(document, options, filename);
        case 'pdf':
          return await this.exportPDF(document, options, filename);
        default:
          throw new Error(`Unsupported format: ${options.format}`);
      }
    } catch (error) {
      return {
        success: false,
        filename,
        error: error instanceof Error ? error.message : 'Export failed',
      };
    }
  }

  private async exportSVG(
    document: VectorDocument,
    options: ExportOptions,
    filename: string
  ): Promise<ExportResult> {
    const { vectorService } = await import('./vectorService');
    const svgString = vectorService.exportToSVG(document);
    
    const blob = new Blob([svgString], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    
    return {
      success: true,
      blob,
      url,
      filename,
    };
  }

  private async exportPNG(
    document: VectorDocument,
    options: ExportOptions,
    filename: string
  ): Promise<ExportResult> {
    const canvas = await this.renderToCanvas(document, options);
    
    return new Promise((resolve) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            resolve({
              success: true,
              blob,
              url,
              filename,
            });
          } else {
            resolve({
              success: false,
              filename,
              error: 'Failed to create PNG blob',
            });
          }
        },
        'image/png',
        options.quality / 100
      );
    });
  }

  private async exportJPG(
    document: VectorDocument,
    options: ExportOptions,
    filename: string
  ): Promise<ExportResult> {
    const canvas = await this.renderToCanvas(document, options);
    
    return new Promise((resolve) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const url = URL.createObjectURL(blob);
            resolve({
              success: true,
              blob,
              url,
              filename,
            });
          } else {
            resolve({
              success: false,
              filename,
              error: 'Failed to create JPG blob',
            });
          }
        },
        'image/jpeg',
        options.quality / 100
      );
    });
  }

  private async exportPDF(
    document: VectorDocument,
    options: ExportOptions,
    filename: string
  ): Promise<ExportResult> {
    // For a full implementation, you'd use a library like jsPDF
    // For now, we'll export as SVG and suggest PDF conversion
    const svgResult = await this.exportSVG(document, options, filename.replace('.pdf', '.svg'));
    
    if (svgResult.success) {
      // Create a simple PDF-like structure (this is a placeholder)
      const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 ${document.width} ${document.height}]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Logo Export) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000010 00000 n 
0000000057 00000 n 
0000000114 00000 n 
0000000225 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
319
%%EOF`;

      const blob = new Blob([pdfContent], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      
      return {
        success: true,
        blob,
        url,
        filename: filename.replace('.svg', '.pdf'),
      };
    }
    
    return svgResult;
  }

  private async renderToCanvas(
    document: VectorDocument,
    options: ExportOptions
  ): Promise<HTMLCanvasElement> {
    const { vectorService } = await import('./vectorService');
    const svgString = vectorService.exportToSVG(document);
    
    const canvas = globalThis.document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    
    // Calculate dimensions
    const width = options.width || document.width;
    const height = options.height || document.height;
    const scale = options.scale;
    
    canvas.width = width * scale;
    canvas.height = height * scale;
    
    // Set background if not transparent
    if (!options.transparent) {
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
    }
    
    // Create SVG data URL
    const svgDataUrl = `data:image/svg+xml;base64,${btoa(svgString)}`;
    
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        resolve(canvas);
      };
      img.onerror = reject;
      img.src = svgDataUrl;
    });
  }

  async downloadFile(result: ExportResult): Promise<void> {
    if (!result.success || !result.url) {
      throw new Error(result.error || 'Export failed');
    }
    
    const link = globalThis.document.createElement('a');
    link.href = result.url;
    link.download = result.filename;
    link.style.display = 'none';
    
    globalThis.document.body.appendChild(link);
    link.click();
    globalThis.document.body.removeChild(link);
    
    // Clean up the URL
    setTimeout(() => {
      URL.revokeObjectURL(result.url!);
    }, 1000);
  }

  getSupportedFormats(): Array<{ value: string; label: string; description: string }> {
    return [
      {
        value: 'svg',
        label: 'SVG',
        description: 'Scalable Vector Graphics - Perfect for web and print',
      },
      {
        value: 'png',
        label: 'PNG',
        description: 'Portable Network Graphics - High quality with transparency',
      },
      {
        value: 'jpg',
        label: 'JPG',
        description: 'JPEG - Compressed format, good for photos',
      },
      {
        value: 'pdf',
        label: 'PDF',
        description: 'Portable Document Format - Professional printing',
      },
    ];
  }

  getPresetSizes(): Array<{ name: string; width: number; height: number; category: string }> {
    return [
      // Web
      { name: 'Web Header', width: 1200, height: 300, category: 'Web' },
      { name: 'Web Logo', width: 400, height: 200, category: 'Web' },
      { name: 'Favicon', width: 32, height: 32, category: 'Web' },
      
      // Social Media
      { name: 'Facebook Cover', width: 1200, height: 630, category: 'Social' },
      { name: 'Instagram Square', width: 1080, height: 1080, category: 'Social' },
      { name: 'Twitter Header', width: 1500, height: 500, category: 'Social' },
      { name: 'LinkedIn Banner', width: 1584, height: 396, category: 'Social' },
      
      // Print
      { name: 'Business Card', width: 1050, height: 600, category: 'Print' },
      { name: 'A4 Document', width: 2480, height: 3508, category: 'Print' },
      { name: 'Letter Size', width: 2550, height: 3300, category: 'Print' },
      
      // Custom
      { name: 'Small', width: 200, height: 200, category: 'Custom' },
      { name: 'Medium', width: 800, height: 600, category: 'Custom' },
      { name: 'Large', width: 1920, height: 1080, category: 'Custom' },
    ];
  }
}

export const exportService = new ExportService();