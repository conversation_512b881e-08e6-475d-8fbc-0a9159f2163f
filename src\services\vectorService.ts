export interface SVGElement {
  id: string;
  type: 'rect' | 'circle' | 'ellipse' | 'path' | 'text' | 'group';
  properties: Record<string, any>;
  style: Record<string, any>;
  children?: SVGElement[];
}

export interface VectorDocument {
  width: number;
  height: number;
  viewBox: string;
  elements: SVGElement[];
  metadata: {
    created: Date;
    modified: Date;
    version: string;
  };
}

class VectorService {
  createDocument(width: number = 800, height: number = 600): VectorDocument {
    return {
      width,
      height,
      viewBox: `0 0 ${width} ${height}`,
      elements: [],
      metadata: {
        created: new Date(),
        modified: new Date(),
        version: '1.0.0',
      },
    };
  }

  createElement(
    type: SVGElement['type'],
    properties: Record<string, any> = {},
    style: Record<string, any> = {}
  ): SVGElement {
    return {
      id: crypto.randomUUID(),
      type,
      properties,
      style,
      children: type === 'group' ? [] : undefined,
    };
  }

  addElement(document: VectorDocument, element: SVGElement): VectorDocument {
    return {
      ...document,
      elements: [...document.elements, element],
      metadata: {
        ...document.metadata,
        modified: new Date(),
      },
    };
  }

  removeElement(document: VectorDocument, elementId: string): VectorDocument {
    const removeFromArray = (elements: SVGElement[]): SVGElement[] => {
      return elements
        .filter(el => el.id !== elementId)
        .map(el => ({
          ...el,
          children: el.children ? removeFromArray(el.children) : undefined,
        }));
    };

    return {
      ...document,
      elements: removeFromArray(document.elements),
      metadata: {
        ...document.metadata,
        modified: new Date(),
      },
    };
  }

  updateElement(
    document: VectorDocument,
    elementId: string,
    updates: Partial<SVGElement>
  ): VectorDocument {
    const updateInArray = (elements: SVGElement[]): SVGElement[] => {
      return elements.map(el => {
        if (el.id === elementId) {
          return { ...el, ...updates };
        }
        return {
          ...el,
          children: el.children ? updateInArray(el.children) : undefined,
        };
      });
    };

    return {
      ...document,
      elements: updateInArray(document.elements),
      metadata: {
        ...document.metadata,
        modified: new Date(),
      },
    };
  }

  exportToSVG(document: VectorDocument): string {
    const renderElement = (element: SVGElement): string => {
      const { type, properties, style, children } = element;
      
      const styleStr = Object.entries(style)
        .map(([key, value]) => `${key}: ${value}`)
        .join('; ');
      
      const propsStr = Object.entries(properties)
        .map(([key, value]) => `${key}="${value}"`)
        .join(' ');
      
      const attributes = [
        propsStr,
        styleStr ? `style="${styleStr}"` : '',
      ].filter(Boolean).join(' ');

      if (type === 'group') {
        const childElements = children?.map(renderElement).join('') || '';
        return `<g ${attributes}>${childElements}</g>`;
      }

      // Handle different SVG element types
      switch (type) {
        case 'rect':
          return `<rect ${attributes} />`;
        case 'circle':
          return `<circle ${attributes} />`;
        case 'ellipse':
          return `<ellipse ${attributes} />`;
        case 'path':
          return `<path ${attributes} />`;
        case 'text':
          return `<text ${attributes}>${properties.content || ''}</text>`;
        default:
          return `<${type} ${attributes} />`;
      }
    };

    const elements = document.elements.map(renderElement).join('\n  ');
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${document.width}" height="${document.height}" viewBox="${document.viewBox}" xmlns="http://www.w3.org/2000/svg">
  ${elements}
</svg>`;
  }

  convertImageToVector(imageUrl: string): Promise<VectorDocument> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        try {
          // Create a simple traced version
          const document = this.createDocument(img.width, img.height);
          
          // For now, create a simple rectangle placeholder
          // In a full implementation, this would use image tracing algorithms
          const rect = this.createElement('rect', {
            x: 0,
            y: 0,
            width: img.width,
            height: img.height,
            fill: '#cccccc',
          });
          
          const result = this.addElement(document, rect);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };
      
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = imageUrl;
    });
  }

  optimizeSVG(document: VectorDocument): VectorDocument {
    // Remove unnecessary elements and optimize paths
    const optimizeElement = (element: SVGElement): SVGElement => {
      const optimized = { ...element };
      
      // Remove default values
      if (optimized.style.fill === 'black') {
        delete optimized.style.fill;
      }
      if (optimized.style.stroke === 'none') {
        delete optimized.style.stroke;
      }
      
      // Optimize children
      if (optimized.children) {
        optimized.children = optimized.children.map(optimizeElement);
      }
      
      return optimized;
    };

    return {
      ...document,
      elements: document.elements.map(optimizeElement),
      metadata: {
        ...document.metadata,
        modified: new Date(),
      },
    };
  }

  calculateBounds(document: VectorDocument): { x: number; y: number; width: number; height: number } {
    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;

    const processElement = (element: SVGElement) => {
      const { type, properties } = element;
      
      switch (type) {
        case 'rect':
          const x = properties.x || 0;
          const y = properties.y || 0;
          const width = properties.width || 0;
          const height = properties.height || 0;
          minX = Math.min(minX, x);
          minY = Math.min(minY, y);
          maxX = Math.max(maxX, x + width);
          maxY = Math.max(maxY, y + height);
          break;
        case 'circle':
          const cx = properties.cx || 0;
          const cy = properties.cy || 0;
          const r = properties.r || 0;
          minX = Math.min(minX, cx - r);
          minY = Math.min(minY, cy - r);
          maxX = Math.max(maxX, cx + r);
          maxY = Math.max(maxY, cy + r);
          break;
        // Add more cases as needed
      }
      
      if (element.children) {
        element.children.forEach(processElement);
      }
    };

    document.elements.forEach(processElement);

    return {
      x: minX === Infinity ? 0 : minX,
      y: minY === Infinity ? 0 : minY,
      width: maxX === -Infinity ? 0 : maxX - minX,
      height: maxY === -Infinity ? 0 : maxY - minY,
    };
  }
}

export const vectorService = new VectorService();