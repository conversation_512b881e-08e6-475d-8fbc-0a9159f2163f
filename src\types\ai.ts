export interface AIGenerationRequest {
  prompt: string;
  style: string;
  colorScheme: 'colorful' | 'monochrome' | 'warm' | 'cool' | 'earth';
  complexity: number;
  aspectRatio?: string;
  format?: 'svg' | 'png' | 'jpg';
}

export interface AIGenerationResponse {
  success: boolean;
  images: GeneratedImage[];
  processingTime: number;
  modelUsed: string;
}

export interface GeneratedImage {
  id: string;
  url: string;
  base64?: string;
  format: string;
  width: number;
  height: number;
  metadata: {
    prompt: string;
    style: string;
    seed?: number;
    model: string;
  };
}

export interface AIModel {
  id: string;
  name: string;
  description: string;
  provider: 'huggingface' | 'openai' | 'replicate' | 'custom';
  endpoint: string;
  capabilities: string[];
  costPerGeneration: number;
  maxResolution: string;
}

export interface StylePreset {
  id: string;
  name: string;
  description: string;
  prompt: string;
  thumbnail: string;
  category: 'modern' | 'vintage' | 'minimal' | 'bold' | 'elegant' | 'playful';
}

export interface ColorPalette {
  id: string;
  name: string;
  colors: string[];
  description: string;
  category: string;
}

export interface GenerationHistory {
  id: string;
  request: AIGenerationRequest;
  response: AIGenerationResponse;
  timestamp: Date;
  projectId: string;
  userId: string;
}

export interface AIProvider {
  name: string;
  apiKey: string;
  endpoint: string;
  models: AIModel[];
  rateLimit: {
    requestsPerMinute: number;
    requestsPerDay: number;
  };
}

export interface PromptTemplate {
  id: string;
  name: string;
  template: string;
  variables: string[];
  category: string;
  examples: string[];
}

export interface AICapabilities {
  textToImage: boolean;
  imageToImage: boolean;
  styleTransfer: boolean;
  upscaling: boolean;
  backgroundRemoval: boolean;
  vectorGeneration: boolean;
}