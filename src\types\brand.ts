export interface BrandProject {
  id: string;
  name: string;
  description: string;
  industry: string;
  targetAudience: string;
  brandValues: string[];
  createdAt: Date;
  updatedAt: Date;
  status: 'draft' | 'in-progress' | 'completed' | 'archived';
  collaborators: string[];
  assets: BrandAsset[];
  guidelines: BrandGuidelines;
}

export interface BrandAsset {
  id: string;
  name: string;
  type: AssetType;
  category: AssetCategory;
  format: string;
  dimensions: Dimensions;
  url: string;
  thumbnail: string;
  metadata: AssetMetadata;
  versions: AssetVersion[];
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export type AssetType = 'logo' | 'business-card' | 'letterhead' | 'social-media' | 'web-banner' | 'print-ad' | 'icon' | 'pattern';

export type AssetCategory = 'primary-logo' | 'secondary-logo' | 'icon-logo' | 'wordmark' | 'stationery' | 'digital' | 'print' | 'merchandise';

export interface Dimensions {
  width: number;
  height: number;
  unit: 'px' | 'mm' | 'cm' | 'in' | 'pt';
}

export interface AssetMetadata {
  colorMode: 'rgb' | 'cmyk' | 'spot';
  dpi?: number;
  fileSize: number;
  colorProfile?: string;
  fonts: string[];
  colors: string[];
  hasTransparency: boolean;
}

export interface AssetVersion {
  id: string;
  version: string;
  url: string;
  thumbnail: string;
  changelog: string;
  createdAt: Date;
  createdBy: string;
}

export interface BrandGuidelines {
  colorPalette: BrandColor[];
  typography: Typography;
  logoUsage: LogoUsage;
  spacing: SpacingGuidelines;
  voice: BrandVoice;
  imagery: ImageryGuidelines;
}

export interface BrandColor {
  id: string;
  name: string;
  hex: string;
  rgb: RGB;
  cmyk: CMYK;
  pantone?: string;
  usage: string[];
  description: string;
}

export interface RGB {
  r: number;
  g: number;
  b: number;
}

export interface CMYK {
  c: number;
  m: number;
  y: number;
  k: number;
}

export interface Typography {
  primary: FontDefinition;
  secondary?: FontDefinition;
  fallbacks: string[];
  sizes: TypographySizes;
  hierarchy: TypographyHierarchy;
}

export interface FontDefinition {
  family: string;
  weights: number[];
  styles: string[];
  url?: string;
  license: string;
}

export interface TypographySizes {
  h1: string;
  h2: string;
  h3: string;
  h4: string;
  body: string;
  small: string;
  caption: string;
}

export interface TypographyHierarchy {
  headings: {
    fontFamily: string;
    fontWeight: number;
    lineHeight: number;
    letterSpacing: number;
  };
  body: {
    fontFamily: string;
    fontWeight: number;
    lineHeight: number;
    letterSpacing: number;
  };
}

export interface LogoUsage {
  minimumSize: Dimensions;
  clearSpace: number;
  colorVariations: LogoVariation[];
  backgrounds: BackgroundUsage[];
  doAndDonts: UsageRule[];
}

export interface LogoVariation {
  name: string;
  description: string;
  assetId: string;
  usage: string[];
}

export interface BackgroundUsage {
  type: 'light' | 'dark' | 'color' | 'image';
  approved: boolean;
  examples: string[];
  notes: string;
}

export interface UsageRule {
  type: 'do' | 'dont';
  title: string;
  description: string;
  example?: string;
}

export interface SpacingGuidelines {
  baseUnit: number;
  scale: number[];
  margins: SpacingValues;
  padding: SpacingValues;
  gridSystem: GridSystem;
}

export interface SpacingValues {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
}

export interface GridSystem {
  columns: number;
  gutter: number;
  maxWidth: number;
  breakpoints: Record<string, number>;
}

export interface BrandVoice {
  tone: string[];
  personality: string[];
  messaging: MessagingGuidelines;
  examples: VoiceExample[];
}

export interface MessagingGuidelines {
  tagline?: string;
  elevator_pitch: string;
  key_messages: string[];
  value_propositions: string[];
}

export interface VoiceExample {
  context: string;
  good_example: string;
  bad_example: string;
  explanation: string;
}

export interface ImageryGuidelines {
  style: string[];
  subjects: string[];
  colors: string[];
  composition: string[];
  mood: string[];
  avoid: string[];
  examples: string[];
}

export interface BrandTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  industry: string[];
  assets: TemplateAsset[];
  preview: string;
  isPremium: boolean;
  price?: number;
}

export interface TemplateAsset {
  type: AssetType;
  dimensions: Dimensions;
  format: string;
  preview: string;
}

export interface ExportOptions {
  formats: string[];
  dimensions: Dimensions[];
  colorModes: string[];
  compression: {
    quality: number;
    optimize: boolean;
  };
  packaging: {
    includeGuidelines: boolean;
    includeSourceFiles: boolean;
    createZip: boolean;
  };
}