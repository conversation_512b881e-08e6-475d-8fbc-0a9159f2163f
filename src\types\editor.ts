export interface EditorState {
  canvas: CanvasState;
  tools: ToolState;
  history: HistoryState;
  selection: SelectionState;
  layers: Layer[];
  artboard: ArtboardState;
}

export interface CanvasState {
  width: number;
  height: number;
  zoom: number;
  pan: Point;
  background: string;
  grid: GridSettings;
  rulers: boolean;
  guides: Guide[];
}

export interface Point {
  x: number;
  y: number;
}

export interface GridSettings {
  enabled: boolean;
  size: number;
  color: string;
  opacity: number;
  snap: boolean;
}

export interface Guide {
  id: string;
  type: 'horizontal' | 'vertical';
  position: number;
  color: string;
  locked: boolean;
}

export interface ToolState {
  activeTool: Tool;
  brushSize: number;
  opacity: number;
  color: string;
  strokeWidth: number;
  fillColor: string;
  strokeColor: string;
  blendMode: BlendMode;
}

export type Tool = 
  | 'select'
  | 'pan'
  | 'zoom'
  | 'rectangle'
  | 'ellipse'
  | 'polygon'
  | 'path'
  | 'text'
  | 'brush'
  | 'eraser'
  | 'eyedropper'
  | 'crop';

export type BlendMode = 
  | 'normal'
  | 'multiply'
  | 'screen'
  | 'overlay'
  | 'darken'
  | 'lighten'
  | 'color-dodge'
  | 'color-burn'
  | 'hard-light'
  | 'soft-light'
  | 'difference'
  | 'exclusion';

export interface HistoryState {
  states: HistoryEntry[];
  currentIndex: number;
  maxStates: number;
}

export interface HistoryEntry {
  id: string;
  name: string;
  timestamp: Date;
  data: any;
  thumbnail?: string;
}

export interface SelectionState {
  selectedIds: string[];
  selectionBounds: Rectangle;
  transformHandles: TransformHandle[];
}

export interface Rectangle {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface TransformHandle {
  id: string;
  type: 'corner' | 'edge' | 'rotation';
  position: Point;
  cursor: string;
}

export interface Layer {
  id: string;
  name: string;
  type: LayerType;
  visible: boolean;
  locked: boolean;
  opacity: number;
  blendMode: BlendMode;
  transform: Transform;
  effects: LayerEffect[];
  data: LayerData;
  children?: Layer[];
}

export type LayerType = 
  | 'group'
  | 'shape'
  | 'text'
  | 'image'
  | 'path'
  | 'symbol'
  | 'mask';

export interface Transform {
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  scaleX: number;
  scaleY: number;
  skewX: number;
  skewY: number;
}

export interface LayerEffect {
  id: string;
  type: EffectType;
  enabled: boolean;
  properties: Record<string, any>;
}

export type EffectType = 
  | 'drop-shadow'
  | 'inner-shadow'
  | 'outer-glow'
  | 'inner-glow'
  | 'bevel-emboss'
  | 'stroke'
  | 'gradient-overlay'
  | 'pattern-overlay'
  | 'color-overlay';

export type LayerData = 
  | ShapeData
  | TextData
  | ImageData
  | PathData
  | GroupData;

export interface ShapeData {
  type: 'rectangle' | 'ellipse' | 'polygon' | 'star';
  fill: Fill;
  stroke: Stroke;
  cornerRadius?: number;
  sides?: number;
  points?: Point[];
}

export interface TextData {
  content: string;
  fontFamily: string;
  fontSize: number;
  fontWeight: number;
  fontStyle: string;
  textAlign: 'left' | 'center' | 'right' | 'justify';
  lineHeight: number;
  letterSpacing: number;
  fill: Fill;
  stroke?: Stroke;
}

export interface ImageData {
  src: string;
  naturalWidth: number;
  naturalHeight: number;
  crop: Rectangle;
  filters: ImageFilter[];
}

export interface PathData {
  d: string;
  fill: Fill;
  stroke: Stroke;
  fillRule: 'nonzero' | 'evenodd';
}

export interface GroupData {
  clipPath?: string;
  mask?: string;
}

export interface Fill {
  type: 'solid' | 'gradient' | 'pattern';
  color?: string;
  gradient?: Gradient;
  pattern?: Pattern;
  opacity: number;
}

export interface Stroke {
  color: string;
  width: number;
  opacity: number;
  dashArray?: number[];
  dashOffset?: number;
  lineCap: 'butt' | 'round' | 'square';
  lineJoin: 'miter' | 'round' | 'bevel';
  miterLimit?: number;
}

export interface Gradient {
  type: 'linear' | 'radial' | 'conic';
  stops: GradientStop[];
  angle?: number;
  center?: Point;
  radius?: number;
}

export interface GradientStop {
  offset: number;
  color: string;
  opacity: number;
}

export interface Pattern {
  type: 'image' | 'svg';
  src: string;
  repeat: 'repeat' | 'repeat-x' | 'repeat-y' | 'no-repeat';
  size: 'cover' | 'contain' | 'auto' | string;
  position: Point;
}

export interface ImageFilter {
  type: 'blur' | 'brightness' | 'contrast' | 'grayscale' | 'hue-rotate' | 'invert' | 'saturate' | 'sepia';
  value: number;
}

export interface ArtboardState {
  width: number;
  height: number;
  units: 'px' | 'mm' | 'cm' | 'in' | 'pt';
  dpi: number;
  colorMode: 'rgb' | 'cmyk';
  background: Fill;
  presets: ArtboardPreset[];
}

export interface ArtboardPreset {
  name: string;
  width: number;
  height: number;
  units: string;
  category: 'web' | 'print' | 'social' | 'mobile' | 'custom';
}

export interface ExportSettings {
  format: 'svg' | 'png' | 'jpg' | 'pdf' | 'eps';
  quality: number;
  width?: number;
  height?: number;
  scale: number;
  transparent: boolean;
  colorMode: 'rgb' | 'cmyk';
  dpi: number;
}

export interface VectorElement {
  id: string;
  type: string;
  properties: Record<string, any>;
  style: Record<string, any>;
  children?: VectorElement[];
}

export interface EditorCommand {
  type: string;
  data: any;
  timestamp: Date;
  userId: string;
}

export interface KeyboardShortcut {
  key: string;
  modifiers: string[];
  action: string;
  description: string;
}